<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Uscreen Purchase Tracking - SportLive24</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }

        .content {
            padding: 40px;
        }

        .test-section {
            margin-bottom: 40px;
            padding: 30px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 5px solid #ff6b35;
        }

        .test-section h2 {
            color: #333;
            margin-bottom: 20px;
            font-size: 1.5em;
        }

        .test-button {
            background: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            margin: 10px 10px 10px 0;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.3);
        }

        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(255, 107, 53, 0.4);
        }

        .test-button:active {
            transform: translateY(0);
        }

        .success-simulation {
            background: #e8f5e8;
            border: 2px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .success-simulation h3 {
            color: #2e7d32;
            margin-bottom: 15px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #333;
        }

        .form-group input {
            width: 100%;
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 5px;
            font-size: 16px;
        }

        .form-group input:focus {
            outline: none;
            border-color: #ff6b35;
        }

        .log-output {
            background: #1e1e1e;
            color: #00ff00;
            padding: 20px;
            border-radius: 10px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            margin: 20px 0;
            white-space: pre-wrap;
        }

        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }

        .status-success { background: #4caf50; }
        .status-error { background: #f44336; }
        .status-warning { background: #ff9800; }
        .status-info { background: #2196f3; }

        .api-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }

        .api-info h3 {
            color: #1976d2;
            margin-bottom: 15px;
        }

        .api-info code {
            background: #f5f5f5;
            padding: 2px 6px;
            border-radius: 3px;
            font-family: 'Courier New', monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🛒 Uscreen Purchase Tracking Test</h1>
            <p>Test stránka pro ověření funkčnosti Uscreen Purchase Code integrace</p>
        </div>

        <div class="content">
            <!-- API Information -->
            <div class="api-info">
                <h3>📡 API Endpoint Information</h3>
                <p><strong>Endpoint:</strong> <code>POST /api/uscreen-purchase-notification/</code></p>
                <p><strong>Purpose:</strong> Příjem notifikací o nákupech z Uscreen checkout stránek</p>
                <p><strong>Integration:</strong> JavaScript kód vložený do Purchase Code sekce v Uscreen</p>
            </div>

            <!-- Manual API Testing -->
            <div class="test-section">
                <h2>🧪 Manuální testování API</h2>
                <p>Testujte API endpoint přímo bez závislosti na Uscreen</p>
                
                <button class="test-button" onclick="testApiEndpoint()">Test API Endpoint</button>
                <button class="test-button" onclick="testApiWithCustomData()">Test s vlastními daty</button>
                <button class="test-button" onclick="testApiErrorHandling()">Test error handling</button>
            </div>

            <!-- Purchase Success Simulation -->
            <div class="test-section">
                <h2>✅ Simulace úspěšného nákupu</h2>
                <p>Simuluje úspěšný nákup na Uscreen checkout stránce</p>
                
                <div class="success-simulation">
                    <h3>🎯 Simulace Purchase Success</h3>
                    <div class="form-group">
                        <label for="sim-email">Email:</label>
                        <input type="email" id="sim-email" value="<EMAIL>" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="sim-product">Product Slug:</label>
                        <input type="text" id="sim-product" value="heroes-gate-31" placeholder="heroes-gate-31">
                    </div>
                    <div class="form-group">
                        <label for="sim-name">Název produktu:</label>
                        <input type="text" id="sim-name" value="Heroes Gate 31" placeholder="Heroes Gate 31">
                    </div>
                    <div class="form-group">
                        <label for="sim-price">Cena (CZK):</label>
                        <input type="number" id="sim-price" value="299" placeholder="299">
                    </div>
                    <div class="form-group">
                        <label for="sim-user">Jméno uživatele:</label>
                        <input type="text" id="sim-user" value="Test User" placeholder="John Doe">
                    </div>
                    
                    <button class="test-button" onclick="simulatePurchaseSuccess()">🚀 Simulovat úspěšný nákup</button>
                </div>
            </div>

            <!-- JavaScript Integration Testing -->
            <div class="test-section">
                <h2>📜 JavaScript Integration Test</h2>
                <p>Testuje JavaScript kód určený pro Purchase Code sekci</p>
                
                <button class="test-button" onclick="loadTrackingScript()">Načíst tracking script</button>
                <button class="test-button" onclick="simulateUscreenEnvironment()">Simulovat Uscreen prostředí</button>
                <button class="test-button" onclick="triggerPurchaseDetection()">Spustit detekci nákupu</button>
                <button class="test-button" onclick="showTrackingLogs()">Zobrazit tracking logy</button>
            </div>

            <!-- Database Verification -->
            <div class="test-section">
                <h2>🗄️ Ověření databáze</h2>
                <p>Kontrola záznamů v databázi po testování</p>
                
                <button class="test-button" onclick="checkRecentPayments()">Zkontrolovat nedávné platby</button>
                <button class="test-button" onclick="openAdminPanel()">Otevřít Django Admin</button>
            </div>

            <!-- Log Output -->
            <div class="test-section">
                <h2>📋 Log Output</h2>
                <div id="log-output" class="log-output">
                    === Uscreen Purchase Tracking Test Log ===
                    Připraven k testování...
                    
                </div>
                <button class="test-button" onclick="clearLogs()">Vymazat logy</button>
            </div>
        </div>
    </div>

    <!-- Load the tracking script for testing -->
    <script src="/static/js/uscreen-purchase-tracking.js"></script>

    <script>
        // Utility functions for logging
        function log(message, type = 'info') {
            const logOutput = document.getElementById('log-output');
            const timestamp = new Date().toLocaleTimeString();
            const statusClass = `status-${type}`;
            
            const logEntry = `[${timestamp}] <span class="status-indicator ${statusClass}"></span>${message}\n`;
            logOutput.innerHTML += logEntry;
            logOutput.scrollTop = logOutput.scrollHeight;
            
            console.log(`[UscreenTest] ${message}`);
        }

        function clearLogs() {
            document.getElementById('log-output').innerHTML = '=== Uscreen Purchase Tracking Test Log ===\nLogy vymazány...\n\n';
        }

        // API Testing Functions
        async function testApiEndpoint() {
            log('🧪 Testování API endpointu...', 'info');
            
            const testData = {
                email: '<EMAIL>',
                product_slug: 'test-product',
                product_name: 'Test Product',
                price: 299.00,
                currency: 'CZK',
                user_name: 'Test User',
                uscreen_user_id: '12345',
                uscreen_purchase_id: `test_${Date.now()}`,
                purchase_timestamp: new Date().toISOString(),
                source: 'manual_test'
            };

            try {
                const response = await fetch('/api/uscreen-purchase-notification/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const responseData = await response.json();

                if (response.ok) {
                    log(`✅ API test úspěšný! Payment ID: ${responseData.payment_id}`, 'success');
                    log(`📄 Response: ${JSON.stringify(responseData, null, 2)}`, 'info');
                } else {
                    log(`❌ API test neúspěšný: ${responseData.error}`, 'error');
                }
            } catch (error) {
                log(`💥 Chyba při API testu: ${error.message}`, 'error');
            }
        }

        async function testApiWithCustomData() {
            const email = document.getElementById('sim-email').value;
            const productSlug = document.getElementById('sim-product').value;
            const productName = document.getElementById('sim-name').value;
            const price = parseFloat(document.getElementById('sim-price').value);
            const userName = document.getElementById('sim-user').value;

            log(`🎯 Testování API s vlastními daty pro ${email}...`, 'info');

            const testData = {
                email: email,
                product_slug: productSlug,
                product_name: productName,
                price: price,
                currency: 'CZK',
                user_name: userName,
                uscreen_purchase_id: `custom_${Date.now()}`,
                purchase_timestamp: new Date().toISOString(),
                source: 'custom_test'
            };

            try {
                const response = await fetch('/api/uscreen-purchase-notification/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(testData)
                });

                const responseData = await response.json();

                if (response.ok) {
                    log(`✅ Custom API test úspěšný! Payment ID: ${responseData.payment_id}`, 'success');
                } else {
                    log(`❌ Custom API test neúspěšný: ${responseData.error}`, 'error');
                }
            } catch (error) {
                log(`💥 Chyba při custom API testu: ${error.message}`, 'error');
            }
        }

        async function testApiErrorHandling() {
            log('🔍 Testování error handling...', 'info');

            // Test s chybějícími daty
            try {
                const response = await fetch('/api/uscreen-purchase-notification/', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify({
                        // Chybí povinná pole
                        price: 299
                    })
                });

                const responseData = await response.json();
                
                if (!response.ok) {
                    log(`✅ Error handling funguje: ${responseData.error}`, 'success');
                } else {
                    log(`⚠️ Error handling nefunguje správně`, 'warning');
                }
            } catch (error) {
                log(`💥 Chyba při error handling testu: ${error.message}`, 'error');
            }
        }

        function simulatePurchaseSuccess() {
            log('🎬 Simulace úspěšného nákupu...', 'info');
            
            // Simulujeme úspěšnou platbu
            const purchaseData = {
                email: document.getElementById('sim-email').value,
                product_slug: document.getElementById('sim-product').value,
                product_name: document.getElementById('sim-name').value,
                price: parseFloat(document.getElementById('sim-price').value),
                currency: 'CZK',
                user_name: document.getElementById('sim-user').value,
                uscreen_purchase_id: `sim_${Date.now()}`,
                purchase_timestamp: new Date().toISOString(),
                source: 'simulation'
            };

            // Použijeme tracking script
            if (window.UscreenPurchaseTracker) {
                window.UscreenPurchaseTracker.purchaseData = purchaseData;
                window.UscreenPurchaseTracker.sendPurchaseNotification();
                log('📤 Notifikace odeslána přes tracking script', 'info');
            } else {
                log('⚠️ Tracking script není načten', 'warning');
                testApiWithCustomData(); // Fallback
            }
        }

        function loadTrackingScript() {
            log('📜 Načítání tracking scriptu...', 'info');
            
            if (window.UscreenPurchaseTracker) {
                log('✅ Tracking script již načten', 'success');
                log(`📊 Status: ${window.UscreenPurchaseTracker.isInitialized ? 'Inicializován' : 'Neinicializován'}`, 'info');
            } else {
                log('❌ Tracking script není dostupný', 'error');
            }
        }

        function simulateUscreenEnvironment() {
            log('🎭 Simulace Uscreen prostředí...', 'info');
            
            // Simulujeme Uscreen objekty
            window.Uscreen = {
                user: {
                    id: '12345',
                    email: document.getElementById('sim-email').value,
                    name: document.getElementById('sim-user').value,
                    first_name: 'Test',
                    last_name: 'User'
                }
            };

            log('✅ Uscreen prostředí simulováno', 'success');
            log(`👤 Simulovaný uživatel: ${window.Uscreen.user.email}`, 'info');
        }

        function triggerPurchaseDetection() {
            log('🔍 Spouštění detekce nákupu...', 'info');
            
            if (window.UscreenPurchaseTracker) {
                // Simulujeme úspěšnou stránku
                document.body.innerHTML += '<div class="success-message" style="display:none;">Payment successful!</div>';
                
                window.UscreenPurchaseTracker.detectSuccessfulPurchase();
                log('🎯 Detekce nákupu spuštěna', 'success');
            } else {
                log('❌ Tracking script není dostupný', 'error');
            }
        }

        function showTrackingLogs() {
            log('📋 Zobrazení tracking logů...', 'info');
            
            if (window.UscreenPurchaseTracker) {
                const tracker = window.UscreenPurchaseTracker;
                log(`🔧 Inicializován: ${tracker.isInitialized}`, 'info');
                log(`📦 Purchase data: ${tracker.purchaseData ? 'Dostupná' : 'Nedostupná'}`, 'info');
                log(`🔄 Retry count: ${tracker.retryCount}`, 'info');
                
                if (tracker.purchaseData) {
                    log(`📄 Data: ${JSON.stringify(tracker.purchaseData, null, 2)}`, 'info');
                }
            } else {
                log('❌ Tracking script není dostupný', 'error');
            }
        }

        async function checkRecentPayments() {
            log('🗄️ Kontrola nedávných plateb...', 'info');
            
            try {
                // Můžeme přidat endpoint pro získání nedávných plateb
                log('ℹ️ Pro kontrolu plateb použijte Django Admin panel', 'info');
                log('🔗 URL: /admin/payments/payment/', 'info');
            } catch (error) {
                log(`💥 Chyba při kontrole plateb: ${error.message}`, 'error');
            }
        }

        function openAdminPanel() {
            log('🔗 Otevírání Django Admin panelu...', 'info');
            window.open('/admin/payments/payment/', '_blank');
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('🚀 Test stránka načtena', 'success');
            log('📋 Připraven k testování Uscreen Purchase Tracking', 'info');
            
            // Auto-load tracking script info
            setTimeout(loadTrackingScript, 1000);
        });
    </script>
</body>
</html>
