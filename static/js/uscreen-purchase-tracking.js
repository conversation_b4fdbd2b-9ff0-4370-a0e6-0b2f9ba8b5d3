/**
 * Uscreen Purchase Tracking Script
 * 
 * Tento script se vkládá do Purchase Code sekce v Uscreen
 * a zachycuje úspěšné platby pro synchronizaci s naším GoPay systémem
 * 
 * Použití:
 * 1. <PERSON><PERSON>žte tento script do Purchase Code sekce v Uscreen admin
 * 2. Script automaticky detekuje úspěšné platby
 * 3. Pošle notifikaci na naše API pro synchronizaci
 */

(function() {
    'use strict';
    
    // Konfigurace
    const CONFIG = {
        apiBaseUrl: 'https://dev.sportlive24.tv/api',
        notificationEndpoint: '/uscreen-purchase-notification/',
        debug: true, // Nastavte na false v produkci
        maxRetries: 3,
        retryDelay: 2000 // 2 sekundy
    };
    
    // Utility funkce pro logování
    function log(message, data = null) {
        if (CONFIG.debug) {
            console.log('[UscreenPurchaseTracking]', message, data || '');
        }
    }
    
    function error(message, data = null) {
        console.error('[UscreenPurchaseTracking]', message, data || '');
    }
    
    // Hlavní třída pro tracking
    class UscreenPurchaseTracker {
        constructor() {
            this.isInitialized = false;
            this.purchaseData = null;
            this.retryCount = 0;
        }
        
        /**
         * Inicializace trackingu
         */
        init() {
            if (this.isInitialized) return;
            
            log('Initializing Uscreen Purchase Tracker...');
            
            try {
                // Detekce úspěšné platby
                this.detectSuccessfulPurchase();
                
                this.isInitialized = true;
                log('Uscreen Purchase Tracker initialized successfully');
                
            } catch (err) {
                error('Failed to initialize Uscreen Purchase Tracker:', err);
            }
        }
        
        /**
         * Detekce úspěšné platby na checkout stránce
         */
        detectSuccessfulPurchase() {
            // Metoda 1: Kontrola URL parametrů
            if (this.checkUrlForSuccess()) {
                log('Purchase success detected via URL');
                this.extractPurchaseDataFromUrl();
                return;
            }
            
            // Metoda 2: Kontrola DOM elementů
            if (this.checkDomForSuccess()) {
                log('Purchase success detected via DOM');
                this.extractPurchaseDataFromDom();
                return;
            }
            
            // Metoda 3: Poslouchání na Uscreen události
            this.listenForUscreenEvents();
            
            // Metoda 4: Polling pro detekci změn
            this.startSuccessPolling();
        }
        
        /**
         * Kontrola URL pro indikátory úspěšné platby
         */
        checkUrlForSuccess() {
            const url = window.location.href;
            const urlParams = new URLSearchParams(window.location.search);
            
            // Typické indikátory úspěšné platby
            return url.includes('/success') || 
                   url.includes('/thank-you') || 
                   url.includes('/confirmation') ||
                   urlParams.get('status') === 'success' ||
                   urlParams.get('payment') === 'success';
        }
        
        /**
         * Kontrola DOM pro indikátory úspěšné platby
         */
        checkDomForSuccess() {
            // Hledáme typické elementy úspěšné platby
            const successSelectors = [
                '.success-message',
                '.payment-success',
                '.thank-you',
                '.confirmation',
                '[data-success="true"]',
                '.purchase-complete'
            ];
            
            for (const selector of successSelectors) {
                if (document.querySelector(selector)) {
                    return true;
                }
            }
            
            // Kontrola textu na stránce
            const bodyText = document.body.textContent.toLowerCase();
            const successKeywords = [
                'payment successful',
                'purchase complete',
                'thank you for your purchase',
                'payment confirmed',
                'order confirmed'
            ];
            
            return successKeywords.some(keyword => bodyText.includes(keyword));
        }
        
        /**
         * Extrakce dat o nákupu z URL
         */
        extractPurchaseDataFromUrl() {
            const urlParams = new URLSearchParams(window.location.search);
            
            this.purchaseData = {
                email: urlParams.get('email') || this.getUserEmail(),
                product_slug: this.extractProductSlug(),
                product_name: urlParams.get('product_name') || this.getProductName(),
                price: parseFloat(urlParams.get('price')) || this.getProductPrice(),
                currency: urlParams.get('currency') || 'CZK',
                user_name: urlParams.get('user_name') || this.getUserName(),
                uscreen_user_id: urlParams.get('user_id') || this.getUscreenUserId(),
                uscreen_purchase_id: urlParams.get('purchase_id') || this.generatePurchaseId(),
                purchase_timestamp: new Date().toISOString(),
                source: 'url_extraction'
            };
            
            log('Purchase data extracted from URL:', this.purchaseData);
            this.sendPurchaseNotification();
        }
        
        /**
         * Extrakce dat o nákupu z DOM
         */
        extractPurchaseDataFromDom() {
            this.purchaseData = {
                email: this.getUserEmail(),
                product_slug: this.extractProductSlug(),
                product_name: this.getProductName(),
                price: this.getProductPrice(),
                currency: 'CZK',
                user_name: this.getUserName(),
                uscreen_user_id: this.getUscreenUserId(),
                uscreen_purchase_id: this.generatePurchaseId(),
                purchase_timestamp: new Date().toISOString(),
                source: 'dom_extraction'
            };
            
            log('Purchase data extracted from DOM:', this.purchaseData);
            this.sendPurchaseNotification();
        }
        
        /**
         * Poslouchání na Uscreen události
         */
        listenForUscreenEvents() {
            // Pokud Uscreen poskytuje JavaScript události
            if (typeof window.Uscreen !== 'undefined') {
                // Posloucháme na možné události
                document.addEventListener('uscreen:purchase:complete', (event) => {
                    log('Uscreen purchase event detected:', event.detail);
                    this.handleUscreenPurchaseEvent(event.detail);
                });
                
                document.addEventListener('uscreen:payment:success', (event) => {
                    log('Uscreen payment event detected:', event.detail);
                    this.handleUscreenPurchaseEvent(event.detail);
                });
            }
        }
        
        /**
         * Zpracování Uscreen události
         */
        handleUscreenPurchaseEvent(eventData) {
            this.purchaseData = {
                email: eventData.email || this.getUserEmail(),
                product_slug: eventData.product_slug || this.extractProductSlug(),
                product_name: eventData.product_name || this.getProductName(),
                price: eventData.price || this.getProductPrice(),
                currency: eventData.currency || 'CZK',
                user_name: eventData.user_name || this.getUserName(),
                uscreen_user_id: eventData.user_id || this.getUscreenUserId(),
                uscreen_purchase_id: eventData.purchase_id || this.generatePurchaseId(),
                purchase_timestamp: new Date().toISOString(),
                source: 'uscreen_event'
            };
            
            log('Purchase data from Uscreen event:', this.purchaseData);
            this.sendPurchaseNotification();
        }
        
        /**
         * Polling pro detekci úspěšné platby
         */
        startSuccessPolling() {
            let pollCount = 0;
            const maxPolls = 30; // 30 sekund
            
            const pollInterval = setInterval(() => {
                pollCount++;
                
                if (this.checkDomForSuccess() && !this.purchaseData) {
                    log('Success detected via polling');
                    this.extractPurchaseDataFromDom();
                    clearInterval(pollInterval);
                }
                
                if (pollCount >= maxPolls) {
                    log('Polling timeout reached');
                    clearInterval(pollInterval);
                }
            }, 1000);
        }
        
        /**
         * Získání emailu uživatele
         */
        getUserEmail() {
            // Pokusíme se najít email v různých místech
            if (typeof window.Uscreen !== 'undefined' && window.Uscreen.user) {
                return window.Uscreen.user.email;
            }
            
            // Hledáme v DOM
            const emailElement = document.querySelector('[data-user-email]') ||
                                document.querySelector('.user-email') ||
                                document.querySelector('input[type="email"]');
            
            if (emailElement) {
                return emailElement.value || emailElement.textContent || emailElement.dataset.userEmail;
            }
            
            return null;
        }
        
        /**
         * Extrakce product slug z URL
         */
        extractProductSlug() {
            const url = window.location.href;
            
            // Pro URL typu: /programs/heroes-gate-31 nebo /products/heroes-gate-31
            const match = url.match(/\/(programs|products)\/([^?\/]+)/);
            if (match && match[2]) {
                return match[2];
            }
            
            // Fallback - pokusíme se najít v title nebo meta tagu
            const titleElement = document.querySelector('title');
            if (titleElement) {
                const title = titleElement.textContent.toLowerCase();
                // Převedeme název na slug
                return title.replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '');
            }
            
            return 'unknown-product';
        }
        
        /**
         * Získání názvu produktu
         */
        getProductName() {
            // Hledáme v různých místech
            const nameElement = document.querySelector('.product-name') ||
                               document.querySelector('.program-title') ||
                               document.querySelector('h1') ||
                               document.querySelector('title');
            
            if (nameElement) {
                return nameElement.textContent.trim();
            }
            
            return 'Unknown Product';
        }
        
        /**
         * Získání ceny produktu
         */
        getProductPrice() {
            // Hledáme cenu v DOM
            const priceElement = document.querySelector('.price') ||
                                document.querySelector('.amount') ||
                                document.querySelector('[data-price]');
            
            if (priceElement) {
                const priceText = priceElement.textContent || priceElement.dataset.price;
                const price = parseFloat(priceText.replace(/[^0-9.,]/g, '').replace(',', '.'));
                return isNaN(price) ? 0 : price;
            }
            
            return 0;
        }
        
        /**
         * Získání jména uživatele
         */
        getUserName() {
            if (typeof window.Uscreen !== 'undefined' && window.Uscreen.user) {
                return window.Uscreen.user.name || 
                       `${window.Uscreen.user.first_name} ${window.Uscreen.user.last_name}`.trim();
            }
            
            const nameElement = document.querySelector('[data-user-name]') ||
                               document.querySelector('.user-name');
            
            if (nameElement) {
                return nameElement.textContent || nameElement.dataset.userName;
            }
            
            // Fallback na email
            const email = this.getUserEmail();
            return email ? email.split('@')[0] : 'Unknown User';
        }
        
        /**
         * Získání Uscreen user ID
         */
        getUscreenUserId() {
            if (typeof window.Uscreen !== 'undefined' && window.Uscreen.user) {
                return window.Uscreen.user.id;
            }
            
            const userIdElement = document.querySelector('[data-user-id]');
            if (userIdElement) {
                return userIdElement.dataset.userId;
            }
            
            return null;
        }
        
        /**
         * Generování purchase ID
         */
        generatePurchaseId() {
            return `uscreen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }
        
        /**
         * Odeslání notifikace o nákupu
         */
        async sendPurchaseNotification() {
            if (!this.purchaseData || !this.purchaseData.email) {
                error('Cannot send notification - missing purchase data or email');
                return;
            }
            
            try {
                log('Sending purchase notification...', this.purchaseData);
                
                const response = await fetch(`${CONFIG.apiBaseUrl}${CONFIG.notificationEndpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(this.purchaseData)
                });
                
                const responseData = await response.json();
                
                if (response.ok) {
                    log('Purchase notification sent successfully:', responseData);
                } else {
                    throw new Error(`API error: ${responseData.error || 'Unknown error'}`);
                }
                
            } catch (err) {
                error('Failed to send purchase notification:', err);
                
                // Retry logic
                if (this.retryCount < CONFIG.maxRetries) {
                    this.retryCount++;
                    log(`Retrying notification (attempt ${this.retryCount}/${CONFIG.maxRetries})...`);
                    
                    setTimeout(() => {
                        this.sendPurchaseNotification();
                    }, CONFIG.retryDelay * this.retryCount);
                } else {
                    error('Max retries reached. Purchase notification failed.');
                }
            }
        }
    }
    
    // Vytvoření globální instance
    window.UscreenPurchaseTracker = new UscreenPurchaseTracker();
    
    // Automatická inicializace
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.UscreenPurchaseTracker.init();
        });
    } else {
        window.UscreenPurchaseTracker.init();
    }
    
    log('Uscreen Purchase Tracking script loaded');
    
})();
