"""
Management command to fetch data from Uscreen API
Based on DJANGO-SL24-REPORT implementation
"""
import requests
import json
import os
import time
import re
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone
from django.utils.text import slugify
from datetime import datetime, timedelta
import pytz
from typing import List, Dict, Optional, Any

from payments.models import (
    UscreenUser, UscreenProgram, UscreenOffer, UscreenVideo,
    UscreenInvoice, UscreenDataSync
)


class Command(BaseCommand):
    help = 'Downloads data from the Uscreen API and saves it to the database.'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.api_key = os.environ.get('USCREEN_API_KEY')
        if not self.api_key:
            raise CommandError("The USCREEN_API_KEY environment variable must be set.")

        self.base_url = "https://www.uscreen.io/publisher_api/v1"
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'accept': 'application/json'
        }

    def generate_unique_slug_from_title(self, title: str, model_class, uscreen_id: int) -> str:
        """
        Generate unique URL-friendly slug from title
        """
        # Handle None or empty title
        if not title or title.strip() == '' or title.lower() == 'none':
            base_slug = f"item-{uscreen_id}"
        else:
            # Use Django's slugify function to create URL-friendly slug
            base_slug = slugify(title.strip())

            # If slugify returns empty string (e.g., only special characters), use uscreen_id
            if not base_slug:
                base_slug = f"item-{uscreen_id}"

        # Limit length to avoid database issues (leave room for counter)
        if len(base_slug) > 190:
            base_slug = base_slug[:190]

        # Check if slug already exists (excluding current item)
        slug = base_slug
        counter = 1

        while model_class.objects.filter(slug=slug).exclude(uscreen_id=uscreen_id).exists():
            slug = f"{base_slug}-{counter}"
            counter += 1

            # Prevent infinite loop
            if counter > 1000:
                slug = f"{base_slug}-{uscreen_id}"
                break

        return slug

    def add_arguments(self, parser):
        parser.add_argument(
            '--type',
            type=str,
            choices=['programs', 'offers', 'videos', 'users', 'invoices', 'all'],
            default='all',
            help='Type of data to fetch (default: all)'
        )
        parser.add_argument(
            '--hours',
            type=int,
            default=24,
            help='Number of hours to look back for data (default: 24)'
        )
        parser.add_argument(
            '--full-sync',
            action='store_true',
            help='Perform full synchronization (ignore time range)'
        )

    def fetch_data(self, endpoint: str, from_date: str = None, to_date: str = None,
                   page: Optional[int] = None, retries: int = 3) -> Optional[List[Dict[str, Any]]]:
        """
        Fetch data from Uscreen API endpoint with pagination
        """
        url = f"{self.base_url}/{endpoint}"
        all_data: List[Dict[str, Any]] = []
        current_page = 1 if page is None else page

        while True:
            try:
                params = {'page': current_page}
                if from_date:
                    params['from'] = from_date
                if to_date:
                    params['to'] = to_date

                self.stdout.write(
                    self.style.NOTICE(f"Fetching {endpoint} page {current_page}...")
                )

                time.sleep(1.5)  # Rate limiting
                response = requests.get(url, headers=self.headers, params=params)
                response.raise_for_status()

                data: List[Dict[str, Any]] = response.json()

                # Handle different response formats
                if endpoint == 'customers':
                    # Customers endpoint returns array directly
                    if not data:  # Empty response = end of pagination
                        break
                    all_data.extend(data)

                    # Log progress for customers
                    self.stdout.write(
                        self.style.SUCCESS(f"Fetched {len(data)} customers from page {current_page}")
                    )

                    # If we got less than expected, probably last page
                    if len(data) < 50:  # Assuming 50 is typical page size
                        break
                else:
                    # Other endpoints might have different structure
                    if not data:  # Empty response = end of pagination
                        break
                    all_data.extend(data)

                if page is not None:  # If specific page requested
                    break

                current_page += 1

            except requests.exceptions.HTTPError as e:
                if e.response and e.response.status_code == 429 and retries > 0:
                    self.stdout.write(self.style.WARNING(
                        f"Rate limited. Retrying in 5 seconds (attempts remaining: {retries})"
                    ))
                    time.sleep(5)
                    retries -= 1
                    continue
                else:
                    self.stdout.write(self.style.ERROR(
                        f"HTTP Error fetching {endpoint}: {e}"
                    ))
                    return None
            except requests.exceptions.RequestException as e:
                self.stdout.write(self.style.ERROR(
                    f"Request Error fetching {endpoint}: {e}"
                ))
                return None
            except json.JSONDecodeError as e:
                self.stdout.write(self.style.ERROR(
                    f"JSON decode error for {endpoint}: {e}"
                ))
                return None

        self.stdout.write(
            self.style.SUCCESS(f"Total {endpoint} fetched: {len(all_data)}")
        )
        return all_data

    @transaction.atomic
    def process_programs(self, data: List[Dict[str, Any]]) -> int:
        """Process and save programs data"""
        if not data:
            return 0

        processed = 0
        for item in data:
            try:
                # Convert Unix timestamp to datetime
                created_at = None
                if item.get('created_at'):
                    created_at = datetime.fromtimestamp(item['created_at'], tz=pytz.utc)

                event_launch_date = None
                if item.get('event_launch_date'):
                    event_launch_date = datetime.fromtimestamp(item['event_launch_date'], tz=pytz.utc)

                # Generate slug from title if not provided by API
                title = item.get('title', '')
                slug = item.get('slug') or self.generate_unique_slug_from_title(title, UscreenProgram, item['id'])

                UscreenProgram.objects.update_or_create(
                    uscreen_id=item['id'],
                    defaults={
                        'title': title,
                        'description': item.get('description', ''),
                        'slug': slug,
                        'part_of_subscription': item.get('part_of_subscription'),
                        'main_poster_url': item.get('main_poster_url'),
                        'secondary_poster_url': item.get('secondary_poster_url'),
                        'price_cents': item.get('price_cents'),
                        'rental_price_cents': item.get('rental_price_cents'),
                        'rental_period': item.get('rental_period'),
                        'rental_period_text': item.get('rental_period_text'),
                        'content_type': item.get('content_type'),
                        'event_launch_date': event_launch_date,
                        'event_state': item.get('event_state'),
                        'author_name': item.get('author_name'),
                        'author_description': item.get('author_description'),
                        'author_image': item.get('author_image'),
                        'preregistration_screen_text': item.get('preregistration_screen_text'),
                        'subscription_plans': item.get('subscription_plans'),
                        'tags': item.get('tags'),
                        'uscreen_created_at': created_at,
                    }
                )
                processed += 1
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f"Error saving program {item.get('id')}: {e}"
                ))

        return processed

    @transaction.atomic
    def process_offers(self, data: List[Dict[str, Any]]) -> int:
        """Process and save offers data"""
        if not data:
            return 0

        processed = 0
        for item in data:
            try:
                created_at = None
                if item.get('created_at'):
                    created_at = datetime.fromtimestamp(item['created_at'], tz=pytz.utc)

                # Generate slug from title if not provided by API
                title = item.get('title', '')
                slug = item.get('slug') or self.generate_unique_slug_from_title(title, UscreenOffer, item['id'])

                UscreenOffer.objects.update_or_create(
                    uscreen_id=item['id'],
                    defaults={
                        'title': title,
                        'description': item.get('description', ''),
                        'slug': slug,
                        'offer_url': item.get('url'),
                        'private': item.get('private'),
                        'price_cents': item.get('price_cents'),
                        'offer_type': item.get('type'),
                        'image_url': item.get('image_url'),
                        'uscreen_created_at': created_at,
                    }
                )
                processed += 1
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f"Error saving offer {item.get('id')}: {e}"
                ))

        return processed

    @transaction.atomic
    def process_videos(self, data: List[Dict[str, Any]]) -> int:
        """Process and save videos data"""
        if not data:
            return 0

        processed = 0
        for item in data:
            try:
                created_at = None
                if item.get('created_at'):
                    created_at = datetime.fromtimestamp(item['created_at'], tz=pytz.utc)

                # Generate slug from title or filename if not provided by API
                title = item.get('title') or item.get('filename') or ''
                slug = item.get('slug') or self.generate_unique_slug_from_title(title, UscreenVideo, item['id'])

                UscreenVideo.objects.update_or_create(
                    uscreen_id=item['id'],
                    defaults={
                        'title': title,
                        'description': item.get('description', ''),
                        'slug': slug,
                        'filename': item.get('filename'),
                        'size': item.get('size'),
                        'duration': item.get('duration'),
                        'source': item.get('source'),
                        'image_url': item.get('image_url'),
                        'chapter_ids': item.get('chapter_ids'),
                        'uscreen_created_at': created_at,
                    }
                )
                processed += 1
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f"Error saving video {item.get('id')}: {e}"
                ))

        return processed

    @transaction.atomic
    def process_users(self, data: List[Dict[str, Any]]) -> int:
        """Process and save users/customers data"""
        if not data:
            return 0

        processed = 0
        for item in data:
            try:
                created_at = None
                if item.get('created_at'):
                    created_at = datetime.fromtimestamp(item['created_at'], tz=pytz.utc)

                # Handle the customer data structure from /customers endpoint
                UscreenUser.objects.update_or_create(
                    uscreen_id=item['id'],
                    defaults={
                        'email': item.get('email', ''),
                        'name': item.get('name', ''),
                        'subscriber': item.get('subscriber', False),
                        'referrer': item.get('referrer', ''),
                        'custom_fields': item.get('custom_fields', {}),
                        'utm_params': item.get('utm_params', {}),
                        'created_at': created_at or timezone.now(),
                    }
                )
                processed += 1

                # Log every 100 processed users
                if processed % 100 == 0:
                    self.stdout.write(
                        self.style.SUCCESS(f"Processed {processed} users so far...")
                    )

            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f"Error saving user {item.get('id')} ({item.get('email', 'unknown')}): {e}"
                ))

        return processed

    @transaction.atomic
    def process_invoices(self, data: List[Dict[str, Any]]) -> int:
        """Process and save invoices data"""
        if not data:
            return 0

        processed = 0
        for item in data:
            try:
                paid_at = None
                if item.get('paid_at'):
                    paid_at = datetime.fromtimestamp(item['paid_at'], tz=pytz.utc)

                created_at = None
                if item.get('created_at'):
                    created_at = datetime.fromtimestamp(item['created_at'], tz=pytz.utc)

                updated_at = None
                if item.get('updated_at'):
                    updated_at = datetime.fromtimestamp(item['updated_at'], tz=pytz.utc)

                UscreenInvoice.objects.update_or_create(
                    uscreen_id=item['id'],
                    defaults={
                        'status': item.get('status', ''),
                        'amount': item.get('amount', 0),
                        'currency': item.get('currency', ''),
                        'user_id': item.get('user_id', 0),
                        'product_id': item.get('product_id', 0),
                        'product_type': item.get('product_type', ''),
                        'paid_at': paid_at,
                        'uscreen_created_at': created_at,
                        'uscreen_updated_at': updated_at,
                        'discount': item.get('discount', 0),
                        'trial': item.get('trial', False),
                        'gift_amount': item.get('gift_amount', 0),
                        'origin': item.get('origin'),
                        'coupon': item.get('coupon'),
                        'localized_currency': item.get('localized_currency'),
                        'localized_amount': item.get('localized_amount'),
                        'localized_discount': item.get('localized_discount'),
                        'localized_gift_amount': item.get('localized_gift_amount'),
                    }
                )
                processed += 1
            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f"Error saving invoice {item.get('id')}: {e}"
                ))

        return processed

    def log_sync_result(self, sync_type: str, success: bool, records_processed: int, error_message: str = None):
        """Log synchronization result"""
        UscreenDataSync.objects.create(
            sync_type=sync_type,
            success=success,
            records_processed=records_processed,
            error_message=error_message
        )

    def handle(self, *args, **options):
        """Main command handler"""
        sync_type = options['type']
        hours_back = options['hours']
        full_sync = options['full_sync']

        # Calculate time range
        from_date = None
        to_date = None

        if not full_sync:
            now = datetime.utcnow()
            from_date = (now - timedelta(hours=hours_back)).strftime('%Y-%m-%dT%H:%M:%SZ')
            to_date = now.strftime('%Y-%m-%dT%H:%M:%SZ')

            self.stdout.write(
                self.style.SUCCESS(f"Fetching data from {from_date} to {to_date}")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS("Performing full synchronization")
            )

        # Define sync operations
        sync_operations = {
            'programs': (self.process_programs, 'programs'),
            'offers': (self.process_offers, 'offers'),
            'videos': (self.process_videos, 'videos'),
            'users': (self.process_users, 'customers'),  # Note: API endpoint is 'customers'
            'invoices': (self.process_invoices, 'invoices'),
        }

        # Determine which operations to run
        if sync_type == 'all':
            operations_to_run = sync_operations.items()
        else:
            operations_to_run = [(sync_type, sync_operations[sync_type])]

        # Execute sync operations
        total_processed = 0
        for operation_name, (processor_func, endpoint) in operations_to_run:
            try:
                self.stdout.write(
                    self.style.SUCCESS(f"Fetching {operation_name}...")
                )

                data = self.fetch_data(endpoint, from_date, to_date)

                if data is not None:
                    processed = processor_func(data)
                    total_processed += processed

                    self.stdout.write(
                        self.style.SUCCESS(f"Processed {processed} {operation_name}")
                    )

                    # Log successful sync
                    self.log_sync_result(operation_name, True, processed)
                else:
                    self.stdout.write(
                        self.style.ERROR(f"Failed to fetch {operation_name}")
                    )
                    # Log failed sync
                    self.log_sync_result(operation_name, False, 0, f"Failed to fetch data from API")

            except Exception as e:
                self.stdout.write(
                    self.style.ERROR(f"Error processing {operation_name}: {e}")
                )
                # Log failed sync
                self.log_sync_result(operation_name, False, 0, str(e))

        self.stdout.write(
            self.style.SUCCESS(f"Synchronization completed. Total records processed: {total_processed}")
        )
