"""
Management command to sync Uscreen customers with proper pagination
Implements the correct API endpoint and hourly sync functionality
"""
import requests
import json
import os
import time
from django.core.management.base import BaseCommand, CommandError
from django.db import transaction
from django.utils import timezone
from datetime import datetime, timedelta
import pytz
from typing import List, Dict, Optional, Any

from payments.models import UscreenUser, UscreenDataSync


class Command(BaseCommand):
    help = 'Sync Uscreen customers using the correct /customers endpoint with pagination'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.api_key = os.environ.get('USCREEN_API_KEY')
        if not self.api_key:
            raise CommandError("The USCREEN_API_KEY environment variable must be set.")

        self.base_url = "https://www.uscreen.io/publisher_api/v1"
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'accept': 'application/json'
        }

    def add_arguments(self, parser):
        parser.add_argument(
            '--hours',
            type=int,
            default=2,
            help='Number of hours to look back for data (default: 2)'
        )
        parser.add_argument(
            '--full-sync',
            action='store_true',
            help='Perform full synchronization (ignore time range)'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be synced without actually saving'
        )

    def fetch_customers_with_pagination(self, from_date=None, to_date=None, dry_run=False):
        """
        Fetch all customers with proper pagination from /customers endpoint
        """
        url = f"{self.base_url}/customers"
        all_customers = []
        seen_customer_ids = set()  # Track seen customer IDs to detect duplicates
        page = 1
        consecutive_duplicate_pages = 0  # Track consecutive pages with same data

        self.stdout.write(
            self.style.SUCCESS(f"Starting customer sync from {url}")
        )

        if from_date and to_date:
            self.stdout.write(
                self.style.NOTICE(f"Time range: {from_date} to {to_date}")
            )

        while True:
            try:
                params = {'page': page}
                if from_date:
                    params['from'] = from_date
                if to_date:
                    params['to'] = to_date

                self.stdout.write(
                    self.style.NOTICE(f"Fetching customers page {page}...")
                )

                # Rate limiting
                time.sleep(1.0)

                response = requests.get(url, headers=self.headers, params=params)
                response.raise_for_status()

                customers_data = response.json()

                # The customers endpoint returns array directly
                if not customers_data or len(customers_data) == 0:
                    self.stdout.write(
                        self.style.SUCCESS(f"✅ No more customers found. Stopping at page {page}")
                    )
                    break

                # Check for duplicate customers (API pagination issue detection)
                current_page_ids = {customer['id'] for customer in customers_data}
                new_customers_in_page = []
                duplicate_count = 0

                for customer in customers_data:
                    if customer['id'] not in seen_customer_ids:
                        new_customers_in_page.append(customer)
                        seen_customer_ids.add(customer['id'])
                    else:
                        duplicate_count += 1

                # If all customers in this page are duplicates, we've likely hit pagination issue
                if duplicate_count == len(customers_data):
                    consecutive_duplicate_pages += 1
                    self.stdout.write(
                        self.style.WARNING(f"⚠️ Page {page}: All {len(customers_data)} customers are duplicates (consecutive: {consecutive_duplicate_pages})")
                    )

                    # If we get 3 consecutive pages of all duplicates, stop
                    if consecutive_duplicate_pages >= 3:
                        self.stdout.write(
                            self.style.SUCCESS(f"✅ Stopping after {consecutive_duplicate_pages} consecutive duplicate pages. API pagination appears to be cycling.")
                        )
                        break
                else:
                    consecutive_duplicate_pages = 0  # Reset counter
                    all_customers.extend(new_customers_in_page)

                    self.stdout.write(
                        self.style.SUCCESS(f"✅ Page {page}: {len(new_customers_in_page)} new customers (duplicates: {duplicate_count})")
                    )

                # Show sample customer data for debugging
                if page == 1 and customers_data:
                    sample_customer = customers_data[0]
                    self.stdout.write(
                        self.style.NOTICE(f"Sample customer: {sample_customer.get('name')} ({sample_customer.get('email')})")
                    )

                page += 1

                # Safety limit to prevent infinite loops
                if page > 1000:
                    self.stdout.write(
                        self.style.WARNING(f"Reached page limit (1000). Stopping.")
                    )
                    break

            except requests.exceptions.HTTPError as e:
                if e.response and e.response.status_code == 429:
                    self.stdout.write(self.style.WARNING(
                        f"Rate limited. Waiting 10 seconds..."
                    ))
                    time.sleep(10)
                    continue
                else:
                    self.stdout.write(self.style.ERROR(
                        f"HTTP Error fetching customers page {page}: {e}"
                    ))
                    break
            except requests.exceptions.RequestException as e:
                self.stdout.write(self.style.ERROR(
                    f"Request Error fetching customers page {page}: {e}"
                ))
                break
            except json.JSONDecodeError as e:
                self.stdout.write(self.style.ERROR(
                    f"JSON decode error for customers page {page}: {e}"
                ))
                break

        self.stdout.write(
            self.style.SUCCESS(f"🎉 Total customers fetched: {len(all_customers)}")
        )
        return all_customers

    @transaction.atomic
    def process_customers(self, customers_data, dry_run=False):
        """Process and save customers data"""
        if not customers_data:
            return 0

        processed = 0
        created = 0
        updated = 0

        for customer in customers_data:
            try:
                created_at = None
                if customer.get('created_at'):
                    created_at = datetime.fromtimestamp(customer['created_at'], tz=pytz.utc)

                customer_data = {
                    'email': customer.get('email', ''),
                    'name': customer.get('name', ''),
                    'subscriber': customer.get('subscriber', False),
                    'referrer': customer.get('referrer', ''),
                    'custom_fields': customer.get('custom_fields', {}),
                    'utm_params': customer.get('utm_params', {}),
                    'created_at': created_at or timezone.now(),
                }

                if dry_run:
                    self.stdout.write(
                        f"DRY RUN: Would process customer {customer['id']} - {customer.get('email')}"
                    )
                    processed += 1
                    continue

                # Handle the customer data structure from /customers endpoint
                uscreen_user, was_created = UscreenUser.objects.update_or_create(
                    uscreen_id=customer['id'],
                    defaults=customer_data
                )

                if was_created:
                    created += 1
                else:
                    updated += 1

                processed += 1

                # Log progress every 100 customers
                if processed % 100 == 0:
                    self.stdout.write(
                        self.style.SUCCESS(f"Processed {processed} customers so far... (Created: {created}, Updated: {updated})")
                    )

            except Exception as e:
                self.stdout.write(self.style.ERROR(
                    f"Error saving customer {customer.get('id')} ({customer.get('email', 'unknown')}): {e}"
                ))

        self.stdout.write(
            self.style.SUCCESS(f"✅ Processing complete: {processed} total, {created} created, {updated} updated")
        )
        return processed

    def log_sync_result(self, success: bool, records_processed: int, error_message: str = None):
        """Log synchronization result"""
        UscreenDataSync.objects.create(
            sync_type='customers',
            success=success,
            records_processed=records_processed,
            error_message=error_message
        )

    def handle(self, *args, **options):
        """Main command handler"""
        hours_back = options['hours']
        full_sync = options['full_sync']
        dry_run = options['dry_run']

        # Calculate time range for incremental sync
        from_date = None
        to_date = None

        if not full_sync:
            now = timezone.now()
            from_datetime = now - timedelta(hours=hours_back)
            to_datetime = now

            # Format for Uscreen API (ISO 8601 with Z suffix)
            from_date = from_datetime.strftime('%Y-%m-%dT%H:%M:%SZ')
            to_date = to_datetime.strftime('%Y-%m-%dT%H:%M:%SZ')

            self.stdout.write(
                self.style.SUCCESS(f"🕐 Incremental sync: {hours_back} hours back")
            )
            self.stdout.write(
                self.style.NOTICE(f"From: {from_date}")
            )
            self.stdout.write(
                self.style.NOTICE(f"To: {to_date}")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS("🔄 Full synchronization mode")
            )

        if dry_run:
            self.stdout.write(
                self.style.WARNING("🧪 DRY RUN MODE - No data will be saved")
            )

        try:
            # Fetch customers data
            customers_data = self.fetch_customers_with_pagination(from_date, to_date, dry_run)

            if customers_data:
                # Process customers
                processed = self.process_customers(customers_data, dry_run)

                if not dry_run:
                    # Log successful sync
                    self.log_sync_result(True, processed)

                self.stdout.write(
                    self.style.SUCCESS(f"🎉 Customer sync completed successfully! Processed: {processed}")
                )
            else:
                self.stdout.write(
                    self.style.WARNING("⚠️ No customers data fetched")
                )
                if not dry_run:
                    self.log_sync_result(False, 0, "No customers data fetched")

        except Exception as e:
            error_msg = f"Customer sync failed: {e}"
            self.stdout.write(
                self.style.ERROR(f"❌ {error_msg}")
            )
            if not dry_run:
                self.log_sync_result(False, 0, error_msg)
            raise
