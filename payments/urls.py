from django.urls import path
from . import views
from . import quick_fix_views

app_name = 'payments'

urlpatterns = [
    # API endpoints
    path('create-payment/', views.create_payment, name='create_payment'),
    path('create-payment-enhanced/', views.create_payment_with_user_check, name='create_payment_enhanced'),
    path('create-payment-from-product/', views.create_payment_from_uscreen_product, name='create_payment_from_product'),
    path('create-payment-get/', views.create_payment_from_product_get, name='create_payment_get'),
    path('check-user/', views.check_user_exists, name='check_user'),
    path('notify/', views.gopay_notification, name='gopay_notification'),
    path('uscreen-purchase-notification/', views.uscreen_purchase_notification, name='uscreen_purchase_notification'),
    path('payment-status/<str:payment_id>/', views.payment_status, name='payment_status'),
    path('product/<str:product_slug>/', views.get_product_info, name='get_product_info'),

    # Success/Error pages
    path('payment-success/', views.payment_success, name='payment_success'),
    path('payment-error/', views.payment_error, name='payment_error'),

    # Test page
    path('test-integration/', views.test_integration, name='test_integration'),
    path('test-uscreen-tracking/', views.test_uscreen_purchase_tracking, name='test_uscreen_tracking'),
    path('test-api-status/', views.test_api_status, name='test_api_status'),
    path('mock-product/<str:product_slug>/', views.mock_product_info, name='mock_product_info'),
    path('mock-create-payment/', views.mock_create_payment, name='mock_create_payment'),
    path('debug/', views.debug_endpoint, name='debug_endpoint'),

    # Quick fix endpoints (work without restart)
    path('quick-test-payment/', quick_fix_views.quick_test_payment, name='quick_test_payment'),
    path('quick-test-status/', quick_fix_views.quick_test_status, name='quick_test_status'),
    path('quick-product/<str:product_slug>/', quick_fix_views.quick_product_info, name='quick_product_info'),
]
