# 🔧 **USCREEN CUSTOMERS SYNC - OPRAVA IMPLEMENTOVÁNA**

## ❌ **Problém byl identifikován**

Původní systém používal špatný API endpoint a neimplementoval správnou paginaci pro získání všech uživatelů z Uscreen.

### **Co bylo špatně:**
- ❌ Používal `/users` endpoint místo `/customers`
- ❌ Neimplementoval správnou paginaci
- ❌ Nezískal všechny uživatele z Uscreen
- ❌ Hodinová synchronizace nezahrnovala uživatele

---

## ✅ **Řešení implementováno**

### **1. 🔧 Opravený UscreenService**

**Soubor:** `payments/services/uscreen_service.py`

- ✅ **Správný endpoint:** `/publisher_api/v1/customers`
- ✅ **Paginace:** Projde všechny stránky automaticky
- ✅ **Rate limiting:** Respektuje API limity
- ✅ **Error handling:** Robustní zpracování chyb

**Nové metody:**
```python
def get_user_by_email(self, email):
    # Po<PERSON>ž<PERSON>v<PERSON> správný /customers endpoint
    
def get_all_customers_with_pagination(self, from_date=None, to_date=None):
    # Projde všechny stránky s paginací
```

### **2. 📜 Nový Management Command**

**Soubor:** `payments/management/commands/sync_uscreen_customers.py`

- ✅ **Správná paginace:** Projde všechny stránky
- ✅ **Časové filtry:** Podporuje `from` a `to` parametry
- ✅ **Progress tracking:** Zobrazuje průběh synchronizace
- ✅ **Dry run režim:** Testování bez ukládání dat

**Použití:**
```bash
# Full sync - všichni uživatelé
python manage.py sync_uscreen_customers --full-sync

# Incremental sync - posledních X hodin
python manage.py sync_uscreen_customers --hours=2

# Test režim
python manage.py sync_uscreen_customers --dry-run --hours=24
```

### **3. 🕐 Aktualizovaný Hourly Sync**

**Soubor:** `payments/management/commands/hourly_sync.py`

- ✅ **Zahrnuje customers:** Synchronizuje uživatele každou hodinu
- ✅ **Správné pořadí:** Customers → Programs → Offers → Videos
- ✅ **Kompletní reporting:** Zobrazuje statistiky pro všechny typy dat

---

## 📊 **Výsledky**

### **Před opravou:**
- 🔴 **Málo uživatelů** - neúplná synchronizace
- 🔴 **Chybějící paginace** - pouze první stránka
- 🔴 **Špatný endpoint** - `/users` místo `/customers`

### **Po opravě:**
- ✅ **65 uživatelů** synchronizováno úspěšně
- ✅ **Kompletní paginace** - všechny stránky
- ✅ **Správný endpoint** - `/customers` s časovými filtry

---

## 🚀 **Jak to nyní funguje**

### **1. Full Synchronization (první spuštění)**
```bash
python manage.py sync_uscreen_customers --full-sync
```
- Stáhne **všechny uživatele** ze všech stránek
- Vytvoří lokální záznamy v databázi
- Výsledek: **65 uživatelů** synchronizováno

### **2. Hourly Synchronization (automatická)**
```bash
python manage.py hourly_sync --hours=2
```
- Stáhne **nové/aktualizované uživatele** za posledních 2 hodin
- Synchronizuje také programs, offers, videos
- Spouští se automaticky každou hodinu přes cron

### **3. API Structure podle dokumentace**

**Request:**
```
GET https://www.uscreen.io/publisher_api/v1/customers?page=1
GET https://www.uscreen.io/publisher_api/v1/customers?from=2025-05-01T00:00:00Z&to=2025-06-01T00:00:00Z
```

**Response:**
```json
[
  {
    "id": 24774621,
    "name": "Aneta Plachá",
    "email": "<EMAIL>",
    "subscriber": false,
    "referrer": "",
    "created_at": 1744470544,
    "custom_fields": {},
    "utm_params": {
      "utm_source": "",
      "utm_medium": "",
      "utm_term": "",
      "utm_content": "",
      "utm_campaign": ""
    },
    "opted_in_for_news_and_updates": true,
    "avatar_url": "https://www.gravatar.com/avatar/...",
    "origin": "web_sign_up"
  }
]
```

---

## 🔄 **Automatická synchronizace**

### **Cron job (každou hodinu):**
```bash
0 * * * * cd /opt/gopay-SL24-payments && source venv/bin/activate && python manage.py hourly_sync >> /var/log/uscreen_hourly_sync.log 2>&1
```

### **Co se synchronizuje každou hodinu:**
1. **👥 Customers** - noví/aktualizovaní uživatelé (posledních 2 hodin)
2. **🎬 Programs** - nové/aktualizované programy
3. **💰 Offers** - nové/aktualizované nabídky  
4. **📹 Videos** - nová/aktualizovaná videa

---

## 🧪 **Testování**

### **1. Test nového customer sync:**
```bash
# Dry run test
python manage.py sync_uscreen_customers --dry-run --hours=24

# Skutečná synchronizace
python manage.py sync_uscreen_customers --hours=2
```

### **2. Test hourly sync:**
```bash
python manage.py hourly_sync --verbose --hours=2
```

### **3. Kontrola databáze:**
```python
from payments.models import UscreenUser
print(f'Total users: {UscreenUser.objects.count()}')
```

---

## 📈 **Monitoring**

### **Django Admin:**
```
https://dev.sportlive24.tv/admin/payments/uscreenuser/
https://dev.sportlive24.tv/admin/payments/uscreendatasync/
```

### **Logy:**
```bash
# Aplikační logy
sudo journalctl -u dev-sportlive24 -f

# Cron logy
tail -f /var/log/uscreen_hourly_sync.log
```

### **Sync statistiky:**
```python
from payments.models import UscreenDataSync
recent_syncs = UscreenDataSync.objects.order_by('-last_sync')[:10]
for sync in recent_syncs:
    print(f"{sync.sync_type}: {sync.records_processed} records - {'✅' if sync.success else '❌'}")
```

---

## 🎯 **Výsledek**

✅ **Problém vyřešen** - nyní získáváme všechny uživatele z Uscreen  
✅ **Správná paginace** - projde všechny stránky automaticky  
✅ **Hodinová synchronizace** - zahrnuje customers + events data  
✅ **Časové filtry** - efektivní incremental sync  
✅ **Monitoring** - kompletní sledování synchronizace  

**🚀 Systém nyní správně synchronizuje všechny Uscreen uživatele!**

---

## 📞 **Další kroky**

1. ✅ **Implementováno** - opravený customer sync
2. ✅ **Testováno** - 65 uživatelů synchronizováno
3. ✅ **Nasazeno** - hourly sync aktualizován
4. 🔄 **Monitorování** - sledujte logy pro ověření funkčnosti

**Synchronizace uživatelů nyní funguje správně!** 🎉
