# 🛒 **USCREEN PURCHASE CODE INTEGRATION**

## 📋 **P<PERSON>ehled**

Kompletní návod pro implementaci automatického trackingu nákupů přímo na Uscreen checkout stránk<PERSON>ch pomocí Purchase Code sekce.

**🎯 Cíl:** Zachytit všechny nákupy provedené přímo na Uscreen a synchronizovat je s naším GoPay systémem pro jednotné reportování.

---

## 🚀 **IMPLEMENTACE**

### **1. Vložení JavaScript kódu do Uscreen**

**Kde:** Uscreen Admin → Settings → Code Injection → **Purchase Code**

**Kód pro vložení:**
```html
<script>
// Uscreen Purchase Tracking Script
// Automaticky zachycuje úspěšné platby a synchronizuje s GoPay systémem

(function() {
    'use strict';

    // Konfigurace
    const CONFIG = {
        apiBaseUrl: 'https://dev.sportlive24.tv/api',
        notificationEndpoint: '/uscreen-purchase-notification/',
        debug: true, // Nastavte na false v produkci
        maxRetries: 3,
        retryDelay: 2000
    };

    // Utility funkce
    function log(message, data = null) {
        if (CONFIG.debug) {
            console.log('[UscreenPurchaseTracking]', message, data || '');
        }
    }

    function error(message, data = null) {
        console.error('[UscreenPurchaseTracking]', message, data || '');
    }

    // Hlavní tracking třída
    class UscreenPurchaseTracker {
        constructor() {
            this.isInitialized = false;
            this.purchaseData = null;
            this.retryCount = 0;
        }

        init() {
            if (this.isInitialized) return;

            log('Initializing Uscreen Purchase Tracker...');

            try {
                this.detectSuccessfulPurchase();
                this.isInitialized = true;
                log('Tracker initialized successfully');
            } catch (err) {
                error('Failed to initialize tracker:', err);
            }
        }

        detectSuccessfulPurchase() {
            // Detekce úspěšné platby
            if (this.checkUrlForSuccess() || this.checkDomForSuccess()) {
                log('Purchase success detected');
                this.extractAndSendPurchaseData();
            } else {
                // Polling pro detekci
                this.startSuccessPolling();
            }
        }

        checkUrlForSuccess() {
            const url = window.location.href;
            return url.includes('/success') ||
                   url.includes('/thank-you') ||
                   url.includes('/confirmation');
        }

        checkDomForSuccess() {
            const successSelectors = [
                '.success-message',
                '.payment-success',
                '.thank-you',
                '.confirmation'
            ];

            return successSelectors.some(selector =>
                document.querySelector(selector)
            );
        }

        startSuccessPolling() {
            let pollCount = 0;
            const maxPolls = 30;

            const pollInterval = setInterval(() => {
                pollCount++;

                if (this.checkDomForSuccess() && !this.purchaseData) {
                    this.extractAndSendPurchaseData();
                    clearInterval(pollInterval);
                }

                if (pollCount >= maxPolls) {
                    clearInterval(pollInterval);
                }
            }, 1000);
        }

        extractAndSendPurchaseData() {
            this.purchaseData = {
                email: this.getUserEmail(),
                product_slug: this.extractProductSlug(),
                product_name: this.getProductName(),
                price: this.getProductPrice(),
                currency: 'CZK',
                user_name: this.getUserName(),
                uscreen_user_id: this.getUscreenUserId(),
                uscreen_purchase_id: this.generatePurchaseId(),
                purchase_timestamp: new Date().toISOString(),
                source: 'uscreen_checkout'
            };

            log('Purchase data extracted:', this.purchaseData);
            this.sendPurchaseNotification();
        }

        getUserEmail() {
            if (typeof window.Uscreen !== 'undefined' && window.Uscreen.user) {
                return window.Uscreen.user.email;
            }

            const emailElement = document.querySelector('input[type="email"]') ||
                                document.querySelector('[data-user-email]');

            return emailElement ?
                   (emailElement.value || emailElement.dataset.userEmail) :
                   null;
        }

        extractProductSlug() {
            const url = window.location.href;
            const match = url.match(/\/(programs|products)\/([^?\/]+)/);

            if (match && match[2]) {
                return match[2];
            }

            // Fallback z title
            const title = document.title.toLowerCase();
            return title.replace(/[^a-z0-9]+/g, '-').replace(/^-|-$/g, '') || 'unknown-product';
        }

        getProductName() {
            const nameElement = document.querySelector('.product-name') ||
                               document.querySelector('h1') ||
                               document.querySelector('title');

            return nameElement ? nameElement.textContent.trim() : 'Unknown Product';
        }

        getProductPrice() {
            const priceElement = document.querySelector('.price') ||
                                document.querySelector('[data-price]');

            if (priceElement) {
                const priceText = priceElement.textContent || priceElement.dataset.price;
                const price = parseFloat(priceText.replace(/[^0-9.,]/g, '').replace(',', '.'));
                return isNaN(price) ? 0 : price;
            }

            return 0;
        }

        getUserName() {
            if (typeof window.Uscreen !== 'undefined' && window.Uscreen.user) {
                return window.Uscreen.user.name ||
                       `${window.Uscreen.user.first_name} ${window.Uscreen.user.last_name}`.trim();
            }

            const email = this.getUserEmail();
            return email ? email.split('@')[0] : 'Unknown User';
        }

        getUscreenUserId() {
            if (typeof window.Uscreen !== 'undefined' && window.Uscreen.user) {
                return window.Uscreen.user.id;
            }

            return null;
        }

        generatePurchaseId() {
            return `uscreen_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
        }

        async sendPurchaseNotification() {
            if (!this.purchaseData || !this.purchaseData.email) {
                error('Cannot send notification - missing data');
                return;
            }

            try {
                log('Sending purchase notification...');

                const response = await fetch(`${CONFIG.apiBaseUrl}${CONFIG.notificationEndpoint}`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    body: JSON.stringify(this.purchaseData)
                });

                const responseData = await response.json();

                if (response.ok) {
                    log('Notification sent successfully:', responseData);
                } else {
                    throw new Error(`API error: ${responseData.error || 'Unknown error'}`);
                }

            } catch (err) {
                error('Failed to send notification:', err);

                if (this.retryCount < CONFIG.maxRetries) {
                    this.retryCount++;
                    setTimeout(() => {
                        this.sendPurchaseNotification();
                    }, CONFIG.retryDelay * this.retryCount);
                }
            }
        }
    }

    // Inicializace
    window.UscreenPurchaseTracker = new UscreenPurchaseTracker();

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            window.UscreenPurchaseTracker.init();
        });
    } else {
        window.UscreenPurchaseTracker.init();
    }

})();
</script>
```

---

## 🔧 **KONFIGURACE**

### **1. Nastavení v Uscreen Admin**

1. **Přihlaste se** do Uscreen admin panelu
2. **Jděte na** Settings → Code Injection
3. **Vyberte** Purchase Code sekci
4. **Vložte** výše uvedený JavaScript kód
5. **Uložte** změny

### **2. Testování**

**Test URL:** Proveďte testovací nákup na vašem Uscreen webu

**Kontrola logů:**
```bash
# Sledování logů našeho API
sudo journalctl -u dev-sportlive24 -f

# Kontrola v browser console
# Otevřete Developer Tools → Console
# Měli byste vidět logy: [UscreenPurchaseTracking]
```

---

## 📊 **API ENDPOINT**

### **Nový endpoint pro příjem notifikací**

```
POST https://dev.sportlive24.tv/api/uscreen-purchase-notification/
```

**Request Body:**
```json
{
    "email": "<EMAIL>",
    "product_slug": "heroes-gate-31",
    "product_name": "Heroes Gate 31",
    "price": 299.00,
    "currency": "CZK",
    "user_name": "John Doe",
    "uscreen_user_id": "12345",
    "uscreen_purchase_id": "uscreen_1642234567890_abc123def",
    "purchase_timestamp": "2024-01-15T10:30:00Z",
    "source": "uscreen_checkout"
}
```

**Response (Success):**
```json
{
    "status": "success",
    "message": "Purchase notification processed successfully",
    "payment_id": 123,
    "uscreen_purchase_id": "uscreen_1642234567890_abc123def"
}
```

**Response (Already Processed):**
```json
{
    "status": "already_processed",
    "message": "Purchase already processed",
    "payment_id": 123
}
```

---

## 🔄 **WORKFLOW**

### **Kompletní proces:**

1. **Uživatel nakupuje** přímo na Uscreen checkout
2. **Uscreen zpracuje** platbu a udělí přístup
3. **Náš JavaScript** detekuje úspěšnou platbu
4. **Pošle notifikaci** na naše API
5. **Naše API vytvoří** záznam platby pro tracking
6. **Synchronizuje data** s lokální databází

### **Výhody:**

✅ **Jednotné reportování** - všechny platby v jednom systému
✅ **Automatická synchronizace** - žádná manuální práce
✅ **Duplikace prevence** - kontrola již zpracovaných nákupů
✅ **Retry mechanismus** - spolehlivé doručení notifikací
✅ **Debug možnosti** - snadné ladění a monitoring

---

## 🧪 **TESTOVÁNÍ**

### **1. Test stránka**

**URL:** https://dev.sportlive24.tv/api/test-uscreen-tracking/

Kompletní test prostředí s:
- ✅ Manuální testování API endpointu
- ✅ Simulace úspěšného nákupu
- ✅ JavaScript integration test
- ✅ Real-time log monitoring
- ✅ Database verification

### **2. Manuální test na Uscreen**

1. Proveďte testovací nákup na Uscreen
2. Zkontrolujte browser console pro logy `[UscreenPurchaseTracking]`
3. Ověřte v Django admin, že se vytvořil Payment záznam

### **3. API test přes curl**

```bash
curl -X POST https://dev.sportlive24.tv/api/uscreen-purchase-notification/ \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "product_slug": "test-product",
    "product_name": "Test Product",
    "price": 299.00,
    "currency": "CZK",
    "user_name": "Test User",
    "uscreen_purchase_id": "test_purchase_123"
  }'
```

### **4. JavaScript console test**

```javascript
// Test v browser console na Uscreen stránce
if (window.UscreenPurchaseTracker) {
    // Simulace nákupu
    window.UscreenPurchaseTracker.purchaseData = {
        email: '<EMAIL>',
        product_slug: 'test-product',
        product_name: 'Test Product',
        price: 299,
        currency: 'CZK',
        user_name: 'Test User',
        uscreen_purchase_id: 'test_' + Date.now(),
        purchase_timestamp: new Date().toISOString(),
        source: 'manual_test'
    };

    // Odeslání notifikace
    window.UscreenPurchaseTracker.sendPurchaseNotification();
}
```

---

## 🔍 **MONITORING**

### **Logy aplikace:**
```bash
sudo journalctl -u dev-sportlive24 -f
```

### **Django admin:**
```
https://dev.sportlive24.tv/admin/payments/payment/
```

### **Browser console:**
- Otevřete Developer Tools
- Sledujte logy s prefixem `[UscreenPurchaseTracking]`

---

## ⚠️ **DŮLEŽITÉ POZNÁMKY**

1. **Purchase Code** se spouští pouze na checkout stránkách
2. **Debug režim** vypněte v produkci (`debug: false`)
3. **API endpoint** musí být dostupný z Uscreen domény
4. **CORS** nastavení může být potřeba upravit
5. **Testujte** na staging prostředí před nasazením

---

## 🚀 **VÝSLEDEK**

Po implementaci budete mít:

✅ **Automatické zachycení** všech Uscreen nákupů
✅ **Synchronizaci** s GoPay systémem
✅ **Jednotné reportování** všech plateb
✅ **Prevenci duplikátů** a retry mechanismus
✅ **Kompletní audit trail** všech transakcí

**🎯 Celý systém bude plně automatický a transparentní!**
